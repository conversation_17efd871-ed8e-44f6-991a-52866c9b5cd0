#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单词消消乐闯关游戏
作者：AI助手
功能：英文单词与中文释义匹配的消消乐游戏
"""

import tkinter as tk
from tkinter import ttk, messagebox
import csv
import random
import re
import threading
import time
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("警告：pyttsx3未安装，语音功能将不可用。可以运行 'pip install pyttsx3' 来安装。")

class WordGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("单词消消乐 - 闯关游戏")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f8ff')
        
        # 游戏数据
        self.words_data = []
        self.current_level = 1
        self.max_level = 1
        self.score = 0
        self.current_words = []
        self.selected_buttons = []
        self.matched_pairs = []
        self.game_mode = "study"  # "study" 或 "challenge"
        
        # 语音引擎
        if TTS_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                self.tts_engine.setProperty('rate', 150)  # 语速
                voices = self.tts_engine.getProperty('voices')
                # 尝试设置英文语音
                for voice in voices:
                    if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            except:
                self.tts_engine = None
                print("语音引擎初始化失败")
        else:
            self.tts_engine = None
        
        # 加载单词数据
        self.load_words()
        
        # 创建界面
        self.create_main_menu()
    
    def load_words(self):
        """加载单词数据从CSV文件"""
        try:
            with open('word.csv', 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                for row in csv_reader:
                    if len(row) >= 2:
                        english_word = row[0].strip()
                        chinese_meaning = row[1].strip()
                        
                        # 清理中文释义，去除词性标记等
                        chinese_meaning = re.sub(r'^[a-zA-Z]+\.\s*', '', chinese_meaning)
                        chinese_meaning = re.sub(r'[；;].*$', '', chinese_meaning)
                        chinese_meaning = chinese_meaning.split('；')[0].split(';')[0]
                        
                        if english_word and chinese_meaning:
                            self.words_data.append({
                                'english': english_word,
                                'chinese': chinese_meaning
                            })
            
            print(f"成功加载 {len(self.words_data)} 个单词")
            self.max_level = min(50, len(self.words_data) // 10)  # 每关10个单词，最多50关
            
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到word.csv文件！")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("错误", f"加载单词文件时出错：{str(e)}")
            self.root.quit()
    
    def create_main_menu(self):
        """创建主菜单界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 标题
        title_label = tk.Label(
            self.root, 
            text="🎮 单词消消乐 🎮", 
            font=("Arial", 28, "bold"),
            bg='#f0f8ff',
            fg='#4169e1'
        )
        title_label.pack(pady=30)
        
        # 副标题
        subtitle_label = tk.Label(
            self.root,
            text="英文单词与中文释义匹配游戏",
            font=("Arial", 16),
            bg='#f0f8ff',
            fg='#666666'
        )
        subtitle_label.pack(pady=10)
        
        # 游戏信息
        info_frame = tk.Frame(self.root, bg='#f0f8ff')
        info_frame.pack(pady=20)
        
        tk.Label(
            info_frame,
            text=f"当前关卡: {self.current_level}",
            font=("Arial", 14),
            bg='#f0f8ff'
        ).pack()
        
        tk.Label(
            info_frame,
            text=f"总分: {self.score}",
            font=("Arial", 14),
            bg='#f0f8ff'
        ).pack()
        
        tk.Label(
            info_frame,
            text=f"可用单词: {len(self.words_data)}",
            font=("Arial", 14),
            bg='#f0f8ff'
        ).pack()
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#f0f8ff')
        button_frame.pack(pady=40)
        
        # 学习模式按钮
        study_btn = tk.Button(
            button_frame,
            text="📚 学习模式",
            font=("Arial", 16, "bold"),
            bg='#32cd32',
            fg='white',
            width=15,
            height=2,
            command=self.start_study_mode
        )
        study_btn.pack(pady=10)
        
        # 挑战模式按钮
        challenge_btn = tk.Button(
            button_frame,
            text="⚡ 挑战模式",
            font=("Arial", 16, "bold"),
            bg='#ff6347',
            fg='white',
            width=15,
            height=2,
            command=self.start_challenge_mode
        )
        challenge_btn.pack(pady=10)
        
        # 关卡选择按钮
        level_btn = tk.Button(
            button_frame,
            text="🎯 选择关卡",
            font=("Arial", 16, "bold"),
            bg='#4169e1',
            fg='white',
            width=15,
            height=2,
            command=self.show_level_selection
        )
        level_btn.pack(pady=10)
        
        # 退出按钮
        quit_btn = tk.Button(
            button_frame,
            text="❌ 退出游戏",
            font=("Arial", 16, "bold"),
            bg='#dc143c',
            fg='white',
            width=15,
            height=2,
            command=self.root.quit
        )
        quit_btn.pack(pady=10)
    
    def show_level_selection(self):
        """显示关卡选择界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="选择关卡",
            font=("Arial", 24, "bold"),
            bg='#f0f8ff',
            fg='#4169e1'
        )
        title_label.pack(pady=20)
        
        # 创建滚动框架
        canvas = tk.Canvas(self.root, bg='#f0f8ff')
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#f0f8ff')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 关卡按钮
        for level in range(1, self.max_level + 1):
            row = (level - 1) // 10
            col = (level - 1) % 10
            
            if row == 0:
                level_frame = tk.Frame(scrollable_frame, bg='#f0f8ff')
                level_frame.pack(pady=10)
            elif col == 0:
                level_frame = tk.Frame(scrollable_frame, bg='#f0f8ff')
                level_frame.pack(pady=10)
            
            btn_color = '#32cd32' if level <= self.current_level else '#cccccc'
            btn_state = 'normal' if level <= self.current_level else 'disabled'
            
            level_btn = tk.Button(
                level_frame,
                text=str(level),
                font=("Arial", 12, "bold"),
                bg=btn_color,
                fg='white',
                width=4,
                height=2,
                state=btn_state,
                command=lambda l=level: self.select_level(l)
            )
            level_btn.pack(side=tk.LEFT, padx=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 返回按钮
        back_btn = tk.Button(
            self.root,
            text="🔙 返回主菜单",
            font=("Arial", 14),
            bg='#4169e1',
            fg='white',
            command=self.create_main_menu
        )
        back_btn.pack(pady=20)
    
    def select_level(self, level):
        """选择关卡"""
        self.current_level = level
        self.create_main_menu()
    
    def start_study_mode(self):
        """开始学习模式"""
        self.game_mode = "study"
        self.prepare_level_words()
        self.show_study_interface()
    
    def start_challenge_mode(self):
        """开始挑战模式"""
        self.game_mode = "challenge"
        self.prepare_level_words()
        self.show_challenge_interface()
    
    def prepare_level_words(self):
        """准备当前关卡的单词"""
        start_idx = (self.current_level - 1) * 10
        end_idx = min(start_idx + 10, len(self.words_data))
        self.current_words = self.words_data[start_idx:end_idx].copy()
        self.matched_pairs = []
        self.selected_buttons = []
    
    def speak_word(self, word):
        """语音播放单词"""
        if self.tts_engine:
            def speak():
                try:
                    self.tts_engine.say(word)
                    self.tts_engine.runAndWait()
                except:
                    pass
            
            # 在新线程中播放语音，避免阻塞界面
            threading.Thread(target=speak, daemon=True).start()
    
    def show_study_interface(self):
        """显示学习模式界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 标题
        title_label = tk.Label(
            self.root,
            text=f"📚 学习模式 - 第 {self.current_level} 关",
            font=("Arial", 20, "bold"),
            bg='#f0f8ff',
            fg='#32cd32'
        )
        title_label.pack(pady=20)
        
        # 说明
        instruction_label = tk.Label(
            self.root,
            text="点击英文单词可以听发音，学习完成后点击开始挑战！",
            font=("Arial", 14),
            bg='#f0f8ff',
            fg='#666666'
        )
        instruction_label.pack(pady=10)
        
        # 单词列表框架
        words_frame = tk.Frame(self.root, bg='#f0f8ff')
        words_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # 创建单词对照表
        for i, word_data in enumerate(self.current_words):
            word_frame = tk.Frame(words_frame, bg='white', relief='raised', bd=2)
            word_frame.pack(fill='x', pady=5, padx=10)
            
            # 英文单词（可点击发音）
            english_btn = tk.Button(
                word_frame,
                text=f"🔊 {word_data['english']}",
                font=("Arial", 14, "bold"),
                bg='#e6f3ff',
                fg='#0066cc',
                relief='flat',
                command=lambda w=word_data['english']: self.speak_word(w)
            )
            english_btn.pack(side='left', padx=10, pady=10)
            
            # 箭头
            arrow_label = tk.Label(
                word_frame,
                text="→",
                font=("Arial", 16, "bold"),
                bg='white',
                fg='#666666'
            )
            arrow_label.pack(side='left', padx=10)
            
            # 中文释义
            chinese_label = tk.Label(
                word_frame,
                text=word_data['chinese'],
                font=("Arial", 14),
                bg='white',
                fg='#333333'
            )
            chinese_label.pack(side='left', padx=10, pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#f0f8ff')
        button_frame.pack(pady=20)
        
        # 开始挑战按钮
        challenge_btn = tk.Button(
            button_frame,
            text="⚡ 开始挑战",
            font=("Arial", 16, "bold"),
            bg='#ff6347',
            fg='white',
            width=12,
            height=2,
            command=self.show_challenge_interface
        )
        challenge_btn.pack(side='left', padx=10)
        
        # 返回主菜单按钮
        back_btn = tk.Button(
            button_frame,
            text="🔙 返回主菜单",
            font=("Arial", 16, "bold"),
            bg='#4169e1',
            fg='white',
            width=12,
            height=2,
            command=self.create_main_menu
        )
        back_btn.pack(side='left', padx=10)

    def show_challenge_interface(self):
        """显示挑战模式界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 标题和信息栏
        header_frame = tk.Frame(self.root, bg='#f0f8ff')
        header_frame.pack(fill='x', pady=10)

        title_label = tk.Label(
            header_frame,
            text=f"⚡ 挑战模式 - 第 {self.current_level} 关",
            font=("Arial", 18, "bold"),
            bg='#f0f8ff',
            fg='#ff6347'
        )
        title_label.pack()

        info_frame = tk.Frame(header_frame, bg='#f0f8ff')
        info_frame.pack(pady=10)

        score_label = tk.Label(
            info_frame,
            text=f"得分: {self.score}",
            font=("Arial", 14),
            bg='#f0f8ff'
        )
        score_label.pack(side='left', padx=20)

        progress_label = tk.Label(
            info_frame,
            text=f"进度: {len(self.matched_pairs)}/{len(self.current_words)}",
            font=("Arial", 14),
            bg='#f0f8ff'
        )
        progress_label.pack(side='left', padx=20)

        # 游戏区域
        game_frame = tk.Frame(self.root, bg='#f0f8ff')
        game_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 创建打乱的单词按钮
        self.create_game_buttons(game_frame)

        # 底部按钮
        bottom_frame = tk.Frame(self.root, bg='#f0f8ff')
        bottom_frame.pack(pady=20)

        restart_btn = tk.Button(
            bottom_frame,
            text="🔄 重新开始",
            font=("Arial", 14),
            bg='#ffa500',
            fg='white',
            command=self.restart_level
        )
        restart_btn.pack(side='left', padx=10)

        back_btn = tk.Button(
            bottom_frame,
            text="🔙 返回主菜单",
            font=("Arial", 14),
            bg='#4169e1',
            fg='white',
            command=self.create_main_menu
        )
        back_btn.pack(side='left', padx=10)

    def create_game_buttons(self, parent):
        """创建游戏按钮"""
        # 准备所有单词（英文和中文）
        all_items = []
        for word_data in self.current_words:
            all_items.append({
                'text': word_data['english'],
                'type': 'english',
                'pair_id': word_data['english']
            })
            all_items.append({
                'text': word_data['chinese'],
                'type': 'chinese',
                'pair_id': word_data['english']
            })

        # 随机打乱
        random.shuffle(all_items)

        # 创建按钮网格
        self.game_buttons = []
        rows = 4
        cols = 5

        for i in range(rows):
            button_row = []
            row_frame = tk.Frame(parent, bg='#f0f8ff')
            row_frame.pack(pady=5)

            for j in range(cols):
                idx = i * cols + j
                if idx < len(all_items):
                    item = all_items[idx]
                    btn = tk.Button(
                        row_frame,
                        text=item['text'],
                        font=("Arial", 12, "bold"),
                        bg='#e6f3ff',
                        fg='#333333',
                        width=15,
                        height=3,
                        wraplength=120,
                        command=lambda b_idx=idx: self.on_button_click(b_idx)
                    )
                    btn.pack(side='left', padx=5, pady=5)

                    # 存储按钮信息
                    btn.item_data = item
                    btn.button_index = idx
                    button_row.append(btn)
                else:
                    # 空按钮
                    empty_btn = tk.Button(
                        row_frame,
                        text="",
                        font=("Arial", 12),
                        bg='#f0f8ff',
                        state='disabled',
                        width=15,
                        height=3
                    )
                    empty_btn.pack(side='left', padx=5, pady=5)
                    button_row.append(empty_btn)

            self.game_buttons.append(button_row)

    def on_button_click(self, button_idx):
        """处理按钮点击事件"""
        # 找到被点击的按钮
        clicked_button = None
        for row in self.game_buttons:
            for btn in row:
                if hasattr(btn, 'button_index') and btn.button_index == button_idx:
                    clicked_button = btn
                    break
            if clicked_button:
                break

        if not clicked_button or not hasattr(clicked_button, 'item_data'):
            return

        # 检查按钮是否已经匹配
        if clicked_button.item_data['pair_id'] in self.matched_pairs:
            return

        # 如果是英文单词，播放发音
        if clicked_button.item_data['type'] == 'english':
            self.speak_word(clicked_button.item_data['text'])

        # 处理选择逻辑
        if clicked_button in self.selected_buttons:
            # 取消选择
            clicked_button.config(bg='#e6f3ff')
            self.selected_buttons.remove(clicked_button)
        else:
            # 选择按钮
            if len(self.selected_buttons) < 2:
                clicked_button.config(bg='#ffeb3b')
                self.selected_buttons.append(clicked_button)

                # 如果选择了两个按钮，检查匹配
                if len(self.selected_buttons) == 2:
                    self.root.after(500, self.check_match)

    def check_match(self):
        """检查两个选中的按钮是否匹配"""
        if len(self.selected_buttons) != 2:
            return

        btn1, btn2 = self.selected_buttons

        # 检查是否为同一对单词
        if btn1.item_data['pair_id'] == btn2.item_data['pair_id']:
            # 匹配成功
            btn1.config(bg='#4caf50', state='disabled')
            btn2.config(bg='#4caf50', state='disabled')
            self.matched_pairs.append(btn1.item_data['pair_id'])
            self.score += 10

            # 检查是否完成关卡
            if len(self.matched_pairs) == len(self.current_words):
                self.level_completed()
        else:
            # 匹配失败
            btn1.config(bg='#f44336')
            btn2.config(bg='#f44336')
            self.root.after(1000, lambda: self.reset_buttons([btn1, btn2]))

        self.selected_buttons = []
        self.update_progress_display()

    def reset_buttons(self, buttons):
        """重置按钮颜色"""
        for btn in buttons:
            if btn.item_data['pair_id'] not in self.matched_pairs:
                btn.config(bg='#e6f3ff')

    def update_progress_display(self):
        """更新进度显示"""
        # 这里可以更新界面上的进度信息
        pass

    def level_completed(self):
        """关卡完成"""
        messagebox.showinfo(
            "恭喜！",
            f"第 {self.current_level} 关完成！\n得分：{self.score}\n"
        )

        # 解锁下一关
        if self.current_level < self.max_level:
            self.current_level += 1

            # 询问是否继续下一关
            if messagebox.askyesno("继续游戏", "是否继续下一关？"):
                self.start_challenge_mode()
            else:
                self.create_main_menu()
        else:
            messagebox.showinfo("游戏完成", "恭喜你完成了所有关卡！")
            self.create_main_menu()

    def restart_level(self):
        """重新开始当前关卡"""
        self.matched_pairs = []
        self.selected_buttons = []
        if self.game_mode == "challenge":
            self.show_challenge_interface()
        else:
            self.show_study_interface()

    def run(self):
        """运行游戏"""
        self.root.mainloop()

if __name__ == "__main__":
    game = WordGame()
    game.run()
