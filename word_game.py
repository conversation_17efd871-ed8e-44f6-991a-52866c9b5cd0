#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单词消消乐闯关游戏
作者：AI助手
功能：英文单词与中文释义匹配的消消乐游戏
"""

import tkinter as tk
from tkinter import ttk, messagebox
import csv
import random
import re
import threading
import time
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("警告：pyttsx3未安装，语音功能将不可用。可以运行 'pip install pyttsx3' 来安装。")

class WordGame:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 单词消消乐 - 闯关游戏")
        self.root.geometry("1200x800")
        self.root.configure(bg='#667eea')

        # 设置窗口图标和样式
        self.root.resizable(True, True)
        self.root.minsize(1000, 700)

        # 游戏数据
        self.words_data = []
        self.current_level = 1
        self.max_level = 1
        self.score = 0
        self.current_words = []
        self.selected_buttons = []
        self.matched_pairs = []
        self.game_mode = "preview"  # "preview" 或 "challenge"
        self.game_buttons = []
        
        # 语音引擎
        if TTS_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                self.tts_engine.setProperty('rate', 150)  # 语速
                voices = self.tts_engine.getProperty('voices')
                # 尝试设置英文语音
                for voice in voices:
                    if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            except:
                self.tts_engine = None
                print("语音引擎初始化失败")
        else:
            self.tts_engine = None
        
        # 加载单词数据
        self.load_words()
        
        # 创建界面
        self.create_main_menu()
    
    def load_words(self):
        """加载单词数据从CSV文件"""
        try:
            with open('word.csv', 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                for row in csv_reader:
                    if len(row) >= 2:
                        english_word = row[0].strip()
                        chinese_meaning = row[1].strip()
                        
                        # 清理中文释义，去除词性标记等
                        chinese_meaning = re.sub(r'^[a-zA-Z]+\.\s*', '', chinese_meaning)
                        chinese_meaning = re.sub(r'[；;].*$', '', chinese_meaning)
                        chinese_meaning = chinese_meaning.split('；')[0].split(';')[0]
                        
                        if english_word and chinese_meaning:
                            self.words_data.append({
                                'english': english_word,
                                'chinese': chinese_meaning
                            })
            
            print(f"成功加载 {len(self.words_data)} 个单词")
            self.max_level = min(50, len(self.words_data) // 10)  # 每关10个单词，最多50关
            
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到word.csv文件！")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("错误", f"加载单词文件时出错：{str(e)}")
            self.root.quit()
    
    def create_main_menu(self):
        """创建主菜单界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 创建渐变背景效果
        main_frame = tk.Frame(self.root, bg='#667eea')
        main_frame.pack(fill='both', expand=True)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#667eea')
        title_frame.pack(pady=50)

        title_label = tk.Label(
            title_frame,
            text="🎮 单词消消乐 🎮",
            font=("Microsoft YaHei", 36, "bold"),
            bg='#667eea',
            fg='white',
            relief='flat'
        )
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="✨ 英文单词与中文释义匹配游戏 ✨",
            font=("Microsoft YaHei", 18),
            bg='#667eea',
            fg='#e8f4fd'
        )
        subtitle_label.pack(pady=10)

        # 游戏信息卡片
        info_card = tk.Frame(main_frame, bg='white', relief='raised', bd=0)
        info_card.pack(pady=30, padx=50, fill='x')

        # 添加圆角效果（通过内边距模拟）
        info_inner = tk.Frame(info_card, bg='white')
        info_inner.pack(padx=20, pady=20, fill='x')

        info_title = tk.Label(
            info_inner,
            text="🎯 游戏信息",
            font=("Microsoft YaHei", 16, "bold"),
            bg='white',
            fg='#333333'
        )
        info_title.pack()

        info_grid = tk.Frame(info_inner, bg='white')
        info_grid.pack(pady=15)

        # 信息项
        info_items = [
            ("🏆 当前关卡", f"{self.current_level}"),
            ("💎 总分", f"{self.score}"),
            ("📚 词汇量", f"{len(self.words_data)}")
        ]

        for i, (label, value) in enumerate(info_items):
            item_frame = tk.Frame(info_grid, bg='white')
            item_frame.pack(side='left', padx=30)

            tk.Label(
                item_frame,
                text=label,
                font=("Microsoft YaHei", 12),
                bg='white',
                fg='#666666'
            ).pack()

            tk.Label(
                item_frame,
                text=value,
                font=("Microsoft YaHei", 16, "bold"),
                bg='white',
                fg='#4169e1'
            ).pack()

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg='#667eea')
        button_frame.pack(pady=40)

        # 开始游戏按钮
        start_btn = tk.Button(
            button_frame,
            text="🚀 开始游戏",
            font=("Microsoft YaHei", 18, "bold"),
            bg='#ff6b6b',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.start_level
        )
        start_btn.pack(pady=15)

        # 选择关卡按钮
        level_btn = tk.Button(
            button_frame,
            text="🎯 选择关卡",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#4ecdc4',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.show_level_selection
        )
        level_btn.pack(pady=10)

        # 退出游戏按钮
        quit_btn = tk.Button(
            button_frame,
            text="❌ 退出游戏",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#95a5a6',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.root.quit
        )
        quit_btn.pack(pady=10)
    
    def show_level_selection(self):
        """显示关卡选择界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = tk.Frame(self.root, bg='#667eea')
        main_frame.pack(fill='both', expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="🎯 选择关卡",
            font=("Microsoft YaHei", 28, "bold"),
            bg='#667eea',
            fg='white'
        )
        title_label.pack(pady=30)

        # 关卡选择区域
        level_container = tk.Frame(main_frame, bg='white', relief='raised', bd=0)
        level_container.pack(pady=20, padx=50, fill='both', expand=True)

        # 创建滚动框架
        canvas = tk.Canvas(level_container, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(level_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 关卡网格
        levels_per_row = 8
        current_row_frame = None

        for level in range(1, min(self.max_level + 1, 101)):  # 限制最多100关显示
            col = (level - 1) % levels_per_row

            if col == 0:
                current_row_frame = tk.Frame(scrollable_frame, bg='white')
                current_row_frame.pack(pady=15, padx=20)

            # 确定按钮状态和颜色
            if level <= self.current_level:
                btn_color = '#4ecdc4'
                btn_fg = 'white'
                btn_state = 'normal'
                btn_text = f"✓ {level}"
            elif level == self.current_level + 1:
                btn_color = '#ff6b6b'
                btn_fg = 'white'
                btn_state = 'normal'
                btn_text = f"▶ {level}"
            else:
                btn_color = '#bdc3c7'
                btn_fg = '#7f8c8d'
                btn_state = 'disabled'
                btn_text = f"🔒 {level}"

            level_btn = tk.Button(
                current_row_frame,
                text=btn_text,
                font=("Microsoft YaHei", 12, "bold"),
                bg=btn_color,
                fg=btn_fg,
                width=8,
                height=3,
                relief='flat',
                cursor='hand2' if btn_state == 'normal' else 'arrow',
                state=btn_state,
                command=lambda l=level: self.select_level(l)
            )
            level_btn.pack(side=tk.LEFT, padx=8, pady=5)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y", pady=20)

        # 返回按钮
        back_btn = tk.Button(
            main_frame,
            text="🔙 返回主菜单",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#95a5a6',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.create_main_menu
        )
        back_btn.pack(pady=20)
    
    def select_level(self, level):
        """选择关卡"""
        self.current_level = level
        self.create_main_menu()

    def start_level(self):
        """开始当前关卡"""
        self.prepare_level_words()
        self.show_preview_interface()
    
    def prepare_level_words(self):
        """准备当前关卡的单词"""
        start_idx = (self.current_level - 1) * 10
        end_idx = min(start_idx + 10, len(self.words_data))
        self.current_words = self.words_data[start_idx:end_idx].copy()
        self.matched_pairs = []
        self.selected_buttons = []
    
    def speak_word(self, word):
        """语音播放单词"""
        if self.tts_engine:
            def speak():
                try:
                    self.tts_engine.say(word)
                    self.tts_engine.runAndWait()
                except:
                    pass
            
            # 在新线程中播放语音，避免阻塞界面
            threading.Thread(target=speak, daemon=True).start()
    
    def show_preview_interface(self):
        """显示关卡预览界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = tk.Frame(self.root, bg='#667eea')
        main_frame.pack(fill='both', expand=True)

        # 标题区域
        header_frame = tk.Frame(main_frame, bg='#667eea')
        header_frame.pack(fill='x', pady=20)

        title_label = tk.Label(
            header_frame,
            text=f"📚 第 {self.current_level} 关 - 单词预览",
            font=("Microsoft YaHei", 24, "bold"),
            bg='#667eea',
            fg='white'
        )
        title_label.pack()

        # 说明文字
        instruction_label = tk.Label(
            header_frame,
            text="🔊 点击英文单词可以听发音，熟悉后开始挑战！",
            font=("Microsoft YaHei", 16),
            bg='#667eea',
            fg='#e8f4fd'
        )
        instruction_label.pack(pady=10)

        # 单词列表容器
        words_container = tk.Frame(main_frame, bg='white', relief='raised', bd=0)
        words_container.pack(pady=20, padx=40, fill='both', expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(words_container, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(words_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 创建单词卡片
        for i, word_data in enumerate(self.current_words):
            # 单词卡片
            card_frame = tk.Frame(scrollable_frame, bg='#f8f9fa', relief='solid', bd=1)
            card_frame.pack(fill='x', pady=8, padx=20)

            # 卡片内容
            content_frame = tk.Frame(card_frame, bg='#f8f9fa')
            content_frame.pack(fill='x', padx=20, pady=15)

            # 序号
            number_label = tk.Label(
                content_frame,
                text=f"{i+1:02d}",
                font=("Microsoft YaHei", 14, "bold"),
                bg='#4ecdc4',
                fg='white',
                width=3,
                height=2
            )
            number_label.pack(side='left', padx=(0, 15))

            # 英文单词（可点击发音）
            english_btn = tk.Button(
                content_frame,
                text=f"🔊 {word_data['english']}",
                font=("Microsoft YaHei", 16, "bold"),
                bg='#e3f2fd',
                fg='#1976d2',
                relief='flat',
                cursor='hand2',
                width=20,
                anchor='w',
                command=lambda w=word_data['english']: self.speak_word(w)
            )
            english_btn.pack(side='left', padx=(0, 20))

            # 箭头
            arrow_label = tk.Label(
                content_frame,
                text="→",
                font=("Microsoft YaHei", 20, "bold"),
                bg='#f8f9fa',
                fg='#4ecdc4'
            )
            arrow_label.pack(side='left', padx=(0, 20))

            # 中文释义
            chinese_label = tk.Label(
                content_frame,
                text=word_data['chinese'],
                font=("Microsoft YaHei", 16),
                bg='#f8f9fa',
                fg='#333333',
                anchor='w'
            )
            chinese_label.pack(side='left', fill='x', expand=True)

        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y", pady=20)

        # 底部按钮区域
        button_frame = tk.Frame(main_frame, bg='#667eea')
        button_frame.pack(pady=30)

        # 开始挑战按钮
        challenge_btn = tk.Button(
            button_frame,
            text="⚡ 开始挑战",
            font=("Microsoft YaHei", 18, "bold"),
            bg='#ff6b6b',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.show_challenge_interface
        )
        challenge_btn.pack(side='left', padx=15)

        # 返回主菜单按钮
        back_btn = tk.Button(
            button_frame,
            text="🔙 返回主菜单",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#95a5a6',
            fg='white',
            width=16,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.create_main_menu
        )
        back_btn.pack(side='left', padx=15)

    def show_challenge_interface(self):
        """显示挑战模式界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 主框架
        main_frame = tk.Frame(self.root, bg='#667eea')
        main_frame.pack(fill='both', expand=True)

        # 标题和信息栏
        header_frame = tk.Frame(main_frame, bg='#667eea')
        header_frame.pack(fill='x', pady=20)

        title_label = tk.Label(
            header_frame,
            text=f"⚡ 挑战模式 - 第 {self.current_level} 关",
            font=("Microsoft YaHei", 24, "bold"),
            bg='#667eea',
            fg='white'
        )
        title_label.pack()

        # 游戏信息面板
        info_panel = tk.Frame(main_frame, bg='white', relief='raised', bd=0)
        info_panel.pack(fill='x', padx=40, pady=(0, 20))

        info_inner = tk.Frame(info_panel, bg='white')
        info_inner.pack(pady=15)

        # 得分显示
        self.score_label = tk.Label(
            info_inner,
            text=f"💎 得分: {self.score}",
            font=("Microsoft YaHei", 16, "bold"),
            bg='white',
            fg='#4ecdc4'
        )
        self.score_label.pack(side='left', padx=30)

        # 进度显示
        self.progress_label = tk.Label(
            info_inner,
            text=f"🎯 进度: {len(self.matched_pairs)}/{len(self.current_words)}",
            font=("Microsoft YaHei", 16, "bold"),
            bg='white',
            fg='#ff6b6b'
        )
        self.progress_label.pack(side='left', padx=30)

        # 剩余单词显示
        remaining = len(self.current_words) - len(self.matched_pairs)
        self.remaining_label = tk.Label(
            info_inner,
            text=f"📝 剩余: {remaining * 2} 个",
            font=("Microsoft YaHei", 16, "bold"),
            bg='white',
            fg='#9b59b6'
        )
        self.remaining_label.pack(side='left', padx=30)

        # 游戏区域
        game_container = tk.Frame(main_frame, bg='white', relief='raised', bd=0)
        game_container.pack(fill='both', expand=True, padx=40, pady=(0, 20))

        self.game_frame = tk.Frame(game_container, bg='white')
        self.game_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 创建打乱的单词按钮
        self.create_game_buttons()

        # 底部按钮
        bottom_frame = tk.Frame(main_frame, bg='#667eea')
        bottom_frame.pack(pady=20)

        restart_btn = tk.Button(
            bottom_frame,
            text="🔄 重新开始",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#f39c12',
            fg='white',
            width=12,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.restart_level
        )
        restart_btn.pack(side='left', padx=15)

        back_btn = tk.Button(
            bottom_frame,
            text="🔙 返回主菜单",
            font=("Microsoft YaHei", 16, "bold"),
            bg='#95a5a6',
            fg='white',
            width=12,
            height=2,
            relief='flat',
            cursor='hand2',
            command=self.create_main_menu
        )
        back_btn.pack(side='left', padx=15)

    def create_game_buttons(self):
        """创建游戏按钮"""
        # 清空游戏区域
        for widget in self.game_frame.winfo_children():
            widget.destroy()

        # 准备未匹配的单词
        unmatched_items = []
        for word_data in self.current_words:
            if word_data['english'] not in self.matched_pairs:
                unmatched_items.append({
                    'text': word_data['english'],
                    'type': 'english',
                    'pair_id': word_data['english']
                })
                unmatched_items.append({
                    'text': word_data['chinese'],
                    'type': 'chinese',
                    'pair_id': word_data['english']
                })

        # 随机打乱
        random.shuffle(unmatched_items)

        # 计算网格布局
        total_items = len(unmatched_items)
        if total_items == 0:
            return

        # 动态计算行列数
        cols = min(6, max(4, int((total_items ** 0.5) * 1.2)))
        rows = (total_items + cols - 1) // cols

        # 创建按钮网格
        self.game_buttons = []
        button_index = 0

        for i in range(rows):
            button_row = []
            row_frame = tk.Frame(self.game_frame, bg='white')
            row_frame.pack(pady=8, expand=True, fill='x')

            # 计算这一行的按钮数量
            buttons_in_row = min(cols, total_items - i * cols)

            # 居中显示按钮
            for j in range(buttons_in_row):
                item = unmatched_items[button_index]

                # 根据类型设置按钮样式
                if item['type'] == 'english':
                    btn_bg = '#e3f2fd'
                    btn_fg = '#1976d2'
                    btn_text = f"🔤 {item['text']}"
                else:
                    btn_bg = '#f3e5f5'
                    btn_fg = '#7b1fa2'
                    btn_text = f"🀄 {item['text']}"

                btn = tk.Button(
                    row_frame,
                    text=btn_text,
                    font=("Microsoft YaHei", 12, "bold"),
                    bg=btn_bg,
                    fg=btn_fg,
                    width=18,
                    height=3,
                    wraplength=140,
                    relief='raised',
                    bd=2,
                    cursor='hand2',
                    command=lambda idx=button_index: self.on_button_click(idx)
                )
                btn.pack(side='left', padx=8, pady=5, expand=True)

                # 存储按钮信息
                btn.item_data = item
                btn.button_index = button_index
                btn.original_bg = btn_bg
                button_row.append(btn)
                button_index += 1

            self.game_buttons.append(button_row)

    def on_button_click(self, button_idx):
        """处理按钮点击事件"""
        # 找到被点击的按钮
        clicked_button = None
        for row in self.game_buttons:
            for btn in row:
                if hasattr(btn, 'button_index') and btn.button_index == button_idx:
                    clicked_button = btn
                    break
            if clicked_button:
                break

        if not clicked_button or not hasattr(clicked_button, 'item_data'):
            return

        # 检查按钮是否已经匹配
        if clicked_button.item_data['pair_id'] in self.matched_pairs:
            return

        # 如果是英文单词，播放发音
        if clicked_button.item_data['type'] == 'english':
            self.speak_word(clicked_button.item_data['text'])

        # 处理选择逻辑
        if clicked_button in self.selected_buttons:
            # 取消选择
            clicked_button.config(bg=clicked_button.original_bg)
            self.selected_buttons.remove(clicked_button)
        else:
            # 选择按钮
            if len(self.selected_buttons) < 2:
                clicked_button.config(bg='#ffd54f', relief='sunken')
                self.selected_buttons.append(clicked_button)

                # 如果选择了两个按钮，检查匹配
                if len(self.selected_buttons) == 2:
                    self.root.after(800, self.check_match)

    def check_match(self):
        """检查两个选中的按钮是否匹配"""
        if len(self.selected_buttons) != 2:
            return

        btn1, btn2 = self.selected_buttons

        # 检查是否为同一对单词
        if btn1.item_data['pair_id'] == btn2.item_data['pair_id']:
            # 匹配成功 - 添加消除动画效果
            btn1.config(bg='#4caf50', fg='white', text='✓ 匹配成功!', relief='flat')
            btn2.config(bg='#4caf50', fg='white', text='✓ 匹配成功!', relief='flat')

            # 添加到匹配列表
            self.matched_pairs.append(btn1.item_data['pair_id'])
            self.score += 20  # 增加得分

            # 延迟后消除按钮并重新布局
            self.root.after(1500, lambda: self.remove_matched_buttons([btn1, btn2]))

        else:
            # 匹配失败
            btn1.config(bg='#f44336', fg='white', text='✗ 错误')
            btn2.config(bg='#f44336', fg='white', text='✗ 错误')
            self.root.after(1500, lambda: self.reset_buttons([btn1, btn2]))

        self.selected_buttons = []
        self.update_progress_display()

    def remove_matched_buttons(self, buttons):
        """移除匹配成功的按钮并重新布局"""
        # 重新创建游戏按钮（不包含已匹配的）
        self.create_game_buttons()

        # 检查是否完成关卡
        if len(self.matched_pairs) == len(self.current_words):
            self.root.after(500, self.level_completed)

    def reset_buttons(self, buttons):
        """重置按钮颜色和文本"""
        for btn in buttons:
            if hasattr(btn, 'item_data') and btn.item_data['pair_id'] not in self.matched_pairs:
                # 恢复原始文本和样式
                if btn.item_data['type'] == 'english':
                    btn.config(
                        bg='#e3f2fd',
                        fg='#1976d2',
                        text=f"🔤 {btn.item_data['text']}",
                        relief='raised'
                    )
                else:
                    btn.config(
                        bg='#f3e5f5',
                        fg='#7b1fa2',
                        text=f"🀄 {btn.item_data['text']}",
                        relief='raised'
                    )

    def update_progress_display(self):
        """更新进度显示"""
        if hasattr(self, 'score_label'):
            self.score_label.config(text=f"💎 得分: {self.score}")

        if hasattr(self, 'progress_label'):
            self.progress_label.config(text=f"🎯 进度: {len(self.matched_pairs)}/{len(self.current_words)}")

        if hasattr(self, 'remaining_label'):
            remaining = len(self.current_words) - len(self.matched_pairs)
            self.remaining_label.config(text=f"📝 剩余: {remaining * 2} 个")

    def level_completed(self):
        """关卡完成"""
        # 计算奖励分数
        bonus_score = max(50, 100 - len(self.matched_pairs) * 5)
        self.score += bonus_score

        completion_msg = f"""
🎉 恭喜完成第 {self.current_level} 关！ 🎉

📊 本关统计：
• 基础得分：{len(self.matched_pairs) * 20}
• 奖励得分：{bonus_score}
• 总得分：{self.score}

🏆 表现评价：{"完美！" if bonus_score >= 80 else "很好！" if bonus_score >= 60 else "不错！"}
        """

        messagebox.showinfo("关卡完成", completion_msg)

        # 解锁下一关
        if self.current_level < self.max_level:
            self.current_level += 1

            # 询问是否继续下一关
            if messagebox.askyesno("继续游戏", "🚀 准备好挑战下一关了吗？"):
                self.start_level()
            else:
                self.create_main_menu()
        else:
            final_msg = f"""
🏆 恭喜你完成了所有关卡！ 🏆

🎯 最终成绩：
• 总得分：{self.score}
• 完成关卡：{self.max_level}
• 学习单词：{self.max_level * 10}

你是真正的单词大师！
            """
            messagebox.showinfo("游戏完成", final_msg)
            self.create_main_menu()

    def restart_level(self):
        """重新开始当前关卡"""
        self.matched_pairs = []
        self.selected_buttons = []
        self.show_preview_interface()

    def run(self):
        """运行游戏"""
        self.root.mainloop()

if __name__ == "__main__":
    game = WordGame()
    game.run()
